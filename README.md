# Homelab 分布式架构

这是一个基于 Ansible / Terraform / Makefile 的分布式 Homelab 架构部署仓库。该架构横跨多个地区，实现了高可用、分布式计算和存储能力，并通过 **Gargantua 项目** 实现跨地域网络互联。

## 架构总览

### 节点分布

| 地区 | 节点名称 | 类型 | 配置 | 用途 |
|------|----------|------|------|------|
| 青岛 | fork | 阿里云 ECS | 2C4G, 50G 云硬盘 | 控制面/容灾流量入口/边缘计算 |
| 张家口 | edge | 阿里云 ECS | 2C2G, 1G 云硬盘 | 常态流量入口/边缘计算 |
| 北京 | batata | Intel NUC11i5 | 4C16G, 1T NVMe SSD | 计算/轻量存储 |
| 天津 | potato | Intel J3455 | 4C8G, 128G SSD + 12T HDD | 计算/重量存储 |
| 北京 | gargantua-beijing | LXC 容器 | 1C512M, 8G 存储 | 软路由/网络互联 |
| 天津 | gargantua-tianjin | LXC 容器 | 1C512M, 8G 存储 | 软路由/网络互联 |

### 网络架构

```mermaid
graph TB
    %% 定义节点
    fork[青岛 fork<br/>控制面/容灾]
    edge[张家口 edge<br/>常态入口]
    batata[北京 batata<br/>计算节点]
    potato[天津 potato<br/>存储节点]

    %% Gargantua 软路由节点
    gargantua_bj[北京 gargantua-beijing<br/>软路由容器]
    gargantua_tj[天津 gargantua-tianjin<br/>软路由容器]

    %% 定义外部设备
    ext[外部设备]

    %% 定义存储服务
    pg[(PostgreSQL)]
    minio[(MinIO)]
    etcd[(Etcd)]
    juicefs[(JuiceFS)]

    %% 定义计算服务
    k3s[k3s集群]
    podman[Podman容器]
    binary[二进制服务]

    %% 连接关系
    %% 公网接入
    fork <--> edge

    %% NBBONE骨干网
    fork <--> batata
    fork <--> potato
    edge <--> batata
    edge <--> potato
    batata <--> potato

    %% Gargantua 网络互联
    batata --> gargantua_bj
    potato --> gargantua_tj
    gargantua_bj <--> gargantua_tj

    %% Headscale授信网络
    ext --> fork
    ext --> edge
    ext --> batata
    ext --> potato
    ext --> gargantua_bj
    ext --> gargantua_tj

    %% 存储层
    potato --> pg
    potato --> minio
    potato --> etcd
    potato --> juicefs
    batata --> pg
    batata --> minio
    batata --> etcd
    batata --> juicefs

    %% 计算层
    potato --> k3s
    batata --> k3s
    fork --> podman
    edge --> binary

    class fork,edge cloud
    class batata,potato home
    class gargantua_bj,gargantua_tj router
    class pg,minio,etcd,juicefs storage
    class k3s,podman,binary compute
```

## 核心功能

### 1. 统一网络架构

- **NBBONE 骨干网**
  - 基于 Wireguard 和自研 `nbbone-agent`
  - DNS-SD / DDNS 实现节点发现
  - 生产流量互通
  - 就近接入策略路由

- **Gargantua 软路由网络**
  - 基于 LXC 容器的轻量级软路由
  - Headscale 私有骨干网互联
  - 跨地域子网打通
  - 支持的网络段：
    - 青岛：192.168.100.0/24
    - 天津：192.168.10.0/24
    - 北京：192.168.11.0/24

- **Headscale 授信网络**
  - 外部设备接入
  - 容灾备份
  - 就近访问
  - 软路由广播本地网络

- **Traefik 网关**
  - 统一服务代理
  - 地域化部署
  - 负载均衡

### 2. 存储服务

- **核心存储**
  - PostgreSQL 数据库
  - MinIO 对象存储
  - Etcd 分布式键值存储
  - JuiceFS 分布式文件系统

- **存储策略**
  - 天津主中心
  - 北京灾备
  - 定期备份同步

### 3. 计算集群

- **Kubernetes (k3s)**
  - 天津/北京分布式部署
  - 单 Master 架构
  - 应用部署和实验

- **其他计算节点**
  - 青岛：Podman 容器
  - 张家口：二进制服务

### 4. 应用生态

- **已部署应用**
  - Gitea (代码仓库/CI/CD)
  - Vaultwarden (密码管理)
  - Memos (笔记)
  - Jellyfin (媒体服务)
  - Wakapi (工作量统计)
  - alist (文件管理)

- **计划部署应用**
  - OpenWebUI / LobeChat
  - Dify
  - 其他实验性应用

### 5. 统一管理入口 (Portal)

- **功能特性**
  - 高可用部署
  - Next.js 开发
  - 基建可视化
  - WebSSH 接入
  - OIDC 认证

### 6. 可观测性

- **监控体系**
  - 统一日志
  - 性能监控
  - 告警系统
  - 链路追踪
  - 安全审计

## Gargantua 项目

### 项目概述

Gargantua 是一个基于 LXC 容器的分布式软路由解决方案，旨在通过 Headscale 私有骨干网实现跨地域局域网互联。

### 架构设计

```mermaid
graph TB
    subgraph "青岛 (192.168.100.0/24)"
        fork[fork 主机]
    end

    subgraph "北京 (192.168.11.0/24)"
        batata[batata 主机]
        gargantua_bj[gargantua-beijing<br/>软路由容器]
        batata --> gargantua_bj
    end

    subgraph "天津 (192.168.10.0/24)"
        potato[potato 主机]
        gargantua_tj[gargantua-tianjin<br/>软路由容器]
        potato --> gargantua_tj
    end

    subgraph "Headscale 骨干网"
        headscale[Headscale 服务器]
        fork <--> headscale
        gargantua_bj <--> headscale
        gargantua_tj <--> headscale
    end

    %% 网络互联
    gargantua_bj -.->|路由广播| gargantua_tj
    gargantua_tj -.->|路由广播| gargantua_bj

    class fork cloud
    class batata,potato home
    class gargantua_bj,gargantua_tj router
    class headscale network
```

### 技术栈

- **基础设施**: Proxmox VE LXC 容器
- **操作系统**: Debian 12 (通过 Packer 构建精简镜像)
- **网络**: Tailscale + Headscale
- **自动化**: Terraform + Ansible
- **路由**: iptables + iproute2

### 功能特性

1. **轻量级部署**: 基于 LXC 容器，资源占用极低
2. **自动化管理**: 完整的 IaC 流程
3. **网络互联**: 通过 Headscale 实现跨地域网络打通
4. **路由广播**: 自动广播本地网络段到 Tailscale 网络
5. **高可用性**: 支持多节点部署和故障转移

### 部署流程

```bash
# 1. 构建 LXC 模板
make build-gargantua-template

# 2. 部署基础设施
make gargantua-full-deploy

# 3. 验证网络连通性
ansible gargantua -i ansible/inventory/hosts.yml -m ping
```

## 部署架构

### 基础设施即代码 (IaC)

- Ansible 用于配置管理
- Terraform 用于基础设施编排
- Makefile 用于任务自动化
- Packer 用于镜像构建

### CI/CD 流水线

- Gitea Actions
- 自动化部署
- 制品管理

## 网络拓扑

```mermaid
graph TB
    %% 定义节点
    fork[青岛 fork]
    edge[张家口 edge]
    batata[北京 batata]
    potato[天津 potato]
    
    %% 定义存储
    hdd[HDD存储<br/>12TB]
    ssd[SSD存储<br/>128GB]
    nvme[NVMe SSD<br/>1TB]
    
    %% 定义网络
    subgraph 公网接入
        fork <--> edge
    end
    
    subgraph 家庭网络
        batata <--> potato
    end
    
    subgraph 存储网络
        potato --> hdd
        potato --> ssd
        batata --> nvme
    end
    
    subgraph 计算网络
        batata --> k3s[k3s集群]
        potato --> k3s
    end
    
    %% 样式
    classDef cloud fill:#f9f,stroke:#333,stroke-width:2px
    classDef home fill:#bbf,stroke:#333,stroke-width:2px
    classDef storage fill:#bfb,stroke:#333,stroke-width:2px
    classDef compute fill:#fbb,stroke:#333,stroke-width:2px
    
    class fork,edge cloud
    class batata,potato home
    class hdd,ssd,nvme storage
    class k3s compute
```

## 部署指南

### 快速开始

1. **克隆仓库**
```bash
git clone https://git.noahgao.net/noah/ops-stack.git
cd ops-stack
```

2. **初始化环境**
```bash
make init
```

3. **配置 Gargantua**
```bash
# 复制配置模板
cp terraform/gargantua/terraform.tfvars.example terraform/gargantua/terraform.tfvars
cp terraform/gargantua/packer/variables.pkrvars.hcl.example terraform/gargantua/packer/variables.pkrvars.hcl

# 编辑配置文件
vim terraform/gargantua/terraform.tfvars
vim terraform/gargantua/packer/variables.pkrvars.hcl
```

4. **部署 Gargantua 项目**
```bash
# 完整部署流程
make gargantua-full-deploy

# 或分步部署
make build-gargantua-template  # 构建 LXC 模板
make gargantua-init           # 初始化 Terraform
make gargantua-apply          # 部署基础设施
make deploy-gargantua         # 配置软路由
```

### 验证部署

```bash
# 检查容器状态
ansible gargantua -i ansible/inventory/hosts.yml -m ping

# 查看路由状态
ansible gargantua -i ansible/inventory/hosts.yml -a "/usr/local/bin/gargantua-status"

# 验证网络连通性
ansible gargantua -i ansible/inventory/hosts.yml -a "tailscale status"
```

### 常用命令

```bash
# Gargantua 相关
make gargantua-plan          # 查看部署计划
make gargantua-apply         # 应用基础设施变更
make deploy-gargantua        # 重新配置软路由
make gargantua-destroy       # 销毁基础设施

# 传统部署
make deploy                  # 完整部署
make deploy-tailscale        # 仅部署 Tailscale
make clean                   # 清理环境
```

.PHONY: init deploy clean deploy-proxmox deploy-cloud deploy-apps deploy-monitoring deploy-qddt deploy-gargantua build-gargantua-template gargantua-init gargantua-plan gargantua-apply gargantua-destroy prepare-local-cache tf-init tf-plan tf-import tf-apply

# 基础目录
ANSIBLE_DIR := ansible
TERRAFORM_DIR := terraform
GARGANTUA_DIR := $(TERRAFORM_DIR)/gargantua

# 初始化
init:
	@echo "初始化项目..."
	ansible-galaxy install -r $(ANSIBLE_DIR)/requirements.yml
	cd $(TERRAFORM_DIR) && terraform init
# Proxmox 虚拟机部署
deploy-proxmox:
	@echo "部署 Proxmox 虚拟机..."
	cd $(TERRAFORM_DIR) && terraform apply -auto-approve

# 云资源部署（仅用于非 Proxmox 管理的节点）
deploy-cloud:
	@echo "配置云资源..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/cloud.yml

# 基础应用部署
deploy-apps:
	@echo "部署基础应用..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/apps.yml

# 监控系统部署
deploy-monitoring:
	@echo "部署监控系统..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/monitoring.yml

# 青岛控制面部署
deploy-qddt:
	@echo "部署青岛控制面..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/qddt.yml$(if $(TAGS), --tags $(TAGS),)

# Tailscale 网络部署
deploy-tailscale:
	@echo "部署 Tailscale 网络..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml$(if $(TAGS), --tags $(TAGS),)

# ==================== Gargantua 项目 ====================

# 构建 Gargantua LXC 模板
build-gargantua-template:
	@echo "构建 Gargantua LXC 模板..."
	cd $(GARGANTUA_DIR)/packer && ./build.sh

# 初始化 Gargantua Terraform
gargantua-init:
	@echo "初始化 Gargantua Terraform..."
	cd $(GARGANTUA_DIR) && terraform init

# 规划 Gargantua 部署
gargantua-plan:
	@echo "规划 Gargantua 部署..."
	cd $(GARGANTUA_DIR) && terraform plan

# 应用 Gargantua 基础设施
gargantua-apply:
	@echo "部署 Gargantua 基础设施..."
	cd $(GARGANTUA_DIR) && terraform apply -auto-approve

# 配置 Gargantua 软路由
deploy-gargantua:
	@echo "配置 Gargantua 软路由..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/gargantua.yml$(if $(TAGS), --tags $(TAGS),)

# 完整 Gargantua 部署流程
gargantua-full-deploy: gargantua-init gargantua-apply deploy-gargantua

# 销毁 Gargantua 基础设施
gargantua-destroy:
	@echo "销毁 Gargantua 基础设施..."
	cd $(GARGANTUA_DIR) && terraform destroy -auto-approve

# 完整部署流程
deploy: init deploy-proxmox deploy-cloud deploy-apps deploy-monitoring deploy-qddt

# 清理环境
clean:
	@echo "清理环境..."
	cd $(TERRAFORM_DIR) && terraform destroy -auto-approve

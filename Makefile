# 基础目录
ANSIBLE_DIR := ansible
TERRAFORM_DIR := terraform
GARGANTUA_DIR := $(TERRAFORM_DIR)/gargantua

# 初始化
.PHONY: init
init:
	@echo "初始化项目..."
	ansible-galaxy install -r $(ANSIBLE_DIR)/requirements.yml
	cd $(TERRAFORM_DIR) && terraform init

# 青岛控制面部署
.PHONY: deploy-qddt
deploy-qddt:
	@echo "部署青岛控制面..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/qddt.yml$(if $(TAGS), --tags $(TAGS),)

# Tailscale 网络部署
.PHONY: deploy-tailscale
deploy-tailscale:
	@echo "部署 Tailscale 网络..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml$(if $(TAGS), --tags $(TAGS),)

# ==================== Gargantua 项目 ====================

# 检查 Gargantua LXC 模板
.PHONY: check-gargantua-template
check-gargantua-template:
	@echo "检查 Gargantua LXC 模板..."
	cd $(GARGANTUA_DIR)/packer && ./simple-build.sh

# 构建 Gargantua LXC 模板 (高级用法)
.PHONY: build-gargantua-template
build-gargantua-template:
	@echo "构建 Gargantua LXC 模板..."
	cd $(GARGANTUA_DIR)/packer && ./build.sh

# 初始化 Gargantua Terraform

gargantua-init:
	@echo "初始化 Gargantua Terraform..."
	cd $(GARGANTUA_DIR) && terraform init

# 规划 Gargantua 部署
gargantua-plan:
	@echo "规划 Gargantua 部署..."
	cd $(GARGANTUA_DIR) && terraform plan

# 应用 Gargantua 基础设施
gargantua-apply:
	@echo "部署 Gargantua 基础设施..."
	cd $(GARGANTUA_DIR) && terraform apply -auto-approve

# 配置 Gargantua 软路由
deploy-gargantua:
	@echo "配置 Gargantua 软路由..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/gargantua.yml$(if $(TAGS), --tags $(TAGS),)

# 完整 Gargantua 部署流程 (使用标准模板)
gargantua-full-deploy: check-gargantua-template gargantua-init gargantua-apply deploy-gargantua

# 完整 Gargantua 部署流程 (使用自定义模板)
gargantua-full-deploy-custom: build-gargantua-template gargantua-init gargantua-apply deploy-gargantua

# 销毁 Gargantua 基础设施
gargantua-destroy:
	@echo "销毁 Gargantua 基础设施..."
	cd $(GARGANTUA_DIR) && terraform destroy -auto-approve

# 完整部署流程
deploy: init deploy-qddt

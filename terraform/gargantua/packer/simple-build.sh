#!/bin/bash

# 简化的 Gargantua 模板构建脚本
# 直接使用现有的 Debian 12 标准模板

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 3 ]; then
    log_error "用法: $0 <proxmox_host> <proxmox_user> <proxmox_password>"
    log_error "示例: $0 ************** root@pam your_password"
    exit 1
fi

PROXMOX_HOST="$1"
PROXMOX_USER="$2"
PROXMOX_PASSWORD="$3"
PROXMOX_NODE="${4:-batata}"

log_info "开始检查 Proxmox 中可用的 LXC 模板..."

# 检查可用的模板
log_info "连接到 Proxmox: $PROXMOX_HOST"
log_info "用户: $PROXMOX_USER"
log_info "节点: $PROXMOX_NODE"

log_warn "请确保以下 Debian 12 模板之一在 Proxmox 中可用："
echo "  - debian-12-standard_12.2-1_amd64.tar.zst"
echo "  - debian-12-standard_12.7-1_amd64.tar.zst"
echo "  - debian-12.2-1-amd64.tar.xz"

log_info "您可以通过以下方式下载模板："
echo "1. 在 Proxmox Web UI 中："
echo "   - 进入节点 -> local (storage) -> CT Templates"
echo "   - 点击 Templates 按钮"
echo "   - 搜索并下载 debian-12-standard"

echo ""
echo "2. 或者通过命令行："
echo "   pveam update"
echo "   pveam available | grep debian-12"
echo "   pveam download local debian-12-standard_12.2-1_amd64.tar.zst"

log_info "模板下载完成后，请更新 terraform/gargantua/main.tf 中的 lxc_template 变量"
log_info "例如: lxc_template = \"local:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst\""

log_info "然后可以直接使用 Terraform 部署容器，无需 Packer 构建自定义模板"

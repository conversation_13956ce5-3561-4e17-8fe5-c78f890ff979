packer {
  required_plugins {
    proxmox = {
      version = ">= 1.1.3"
      source  = "github.com/hashicorp/proxmox"
    }
  }
}

# 变量定义
variable "proxmox_api_url" {
  type        = string
  description = "Proxmox API URL"
  default     = "https://**************:8006/api2/json"
}

variable "proxmox_username" {
  type        = string
  description = "Proxmox username"
  default     = "root@pam"
}

variable "proxmox_password" {
  type        = string
  description = "Proxmox password"
  sensitive   = true
}

variable "proxmox_node" {
  type        = string
  description = "Proxmox node name"
  default     = "batata"
}

variable "template_name" {
  type        = string
  description = "Template name"
  default     = "debian-12-gargantua"
}

# Debian 12 LXC 模板构建
source "proxmox-clone" "debian-gargantua" {
  # Proxmox 连接配置
  proxmox_url              = var.proxmox_api_url
  username                 = var.proxmox_username
  password                 = var.proxmox_password
  insecure_skip_tls_verify = true
  
  # 节点和模板配置
  node                 = var.proxmox_node
  clone_vm_id          = 9000  # 基础 Debian 12 模板 ID
  template_name        = var.template_name
  template_description = "Debian 12 optimized for Gargantua router containers"
  
  # 虚拟机配置
  cores    = 1
  memory   = 512
  
  # 网络配置
  network_adapters {
    bridge = "vmbr0"
    model  = "virtio"
  }
  
  # 磁盘配置
  disks {
    disk_size    = "8G"
    storage_pool = "local-lvm"
    type         = "scsi"
  }
  
  # SSH 配置
  ssh_username = "root"
  ssh_password = "packer"
  ssh_timeout = "15m"
}

# 构建配置
build {
  sources = ["source.proxmox-clone.debian-gargantua"]
  
  # 更新系统
  provisioner "shell" {
    inline = [
      "apt-get update",
      "apt-get upgrade -y"
    ]
  }
  
  # 安装必要的软件包
  provisioner "shell" {
    inline = [
      "apt-get install -y curl wget gnupg2 software-properties-common",
      "apt-get install -y iptables iproute2 iputils-ping net-tools",
      "apt-get install -y systemd-resolved openssh-server",
      "apt-get install -y python3 python3-pip",
      "apt-get install -y htop vim nano"
    ]
  }
  
  # 启用 IP 转发
  provisioner "shell" {
    inline = [
      "echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf",
      "echo 'net.ipv6.conf.all.forwarding=1' >> /etc/sysctl.conf",
      "sysctl -p"
    ]
  }
  
  # 配置 SSH
  provisioner "shell" {
    inline = [
      "systemctl enable ssh",
      "sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config",
      "sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config"
    ]
  }
  
  # 清理系统
  provisioner "shell" {
    inline = [
      "apt-get autoremove -y",
      "apt-get autoclean",
      "rm -rf /var/lib/apt/lists/*",
      "rm -rf /tmp/*",
      "rm -rf /var/tmp/*",
      "history -c"
    ]
  }
  
  # 创建 LXC 模板
  post-processor "shell-local" {
    inline = [
      "echo 'Debian 12 Gargantua template created successfully'"
    ]
  }
}

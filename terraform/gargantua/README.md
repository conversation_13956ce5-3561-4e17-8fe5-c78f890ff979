# Gargantua 软路由项目

## 项目简介

Gargantua 是一个基于 LXC 容器的分布式软路由解决方案，通过 Headscale 私有骨干网实现跨地域局域网互联。该项目在 unsh 和 sh 两个地域的 Proxmox VE 主机上部署轻量级软路由容器，实现以下网络段的互联：

- qddt：192.168.100.0/24
- sh：************/24
- unsh：************/24

## 架构设计

### 网络拓扑

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   qddt fork     │    │  unsh batata    │    │   sh potato     │
│ 192.168.100.0/24│    │ ************/24 │    │ ************/24 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │              ┌───────▼───────┐      ┌───────▼───────┐
          │              │ interstellar  │      │    cooper     │
          │              │   软路由容器   │      │   软路由容器   │
          │              └───────┬───────┘      └───────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                         ┌───────▼───────┐
                         │  Headscale    │
                         │   骨干网络     │
                         └───────────────┘
```

### 组件说明

1. **LXC 容器**: 运行在 Proxmox VE 上的轻量级容器
2. **Debian 12**: 通过 Packer 构建的精简系统镜像
3. **Tailscale**: 提供 WireGuard 基础的网络连接
4. **Headscale**: 自托管的 Tailscale 控制服务器
5. **iptables**: 处理 NAT 和路由规则

## 目录结构

```
terraform/gargantua/
├── main.tf                    # 主要的 Terraform 配置
├── variables.tf               # 变量定义
├── outputs.tf                 # 输出定义
├── terraform.tfvars.example   # 配置示例
├── packer/                    # Packer 镜像构建
│   ├── debian-gargantua.pkr.hcl
│   ├── build.sh
│   └── variables.pkrvars.hcl.example
└── README.md                  # 本文档
```

## 部署步骤

### 1. 准备工作

确保已安装以下工具：
- Terraform >= 1.0
- Packer >= 1.8
- Ansible >= 2.9

### 2. 配置文件

复制并编辑配置文件：

```bash
cp terraform.tfvars.example terraform.tfvars
cp packer/variables.pkrvars.hcl.example packer/variables.pkrvars.hcl
```

编辑 `terraform.tfvars`：
```hcl
# Proxmox 连接配置
proxmox_batata_api_url = "https://**************:8006/api2/json"
proxmox_potato_api_url = "https://************00:8006/api2/json"
proxmox_user          = "root@pam"
proxmox_password      = "your_password"

# SSH 公钥
ssh_public_keys = "ssh-rsa AAAAB3NzaC1yc2E... your_key"

# LXC 密码
lxc_password = "your_lxc_password"
```

### 3. 构建 LXC 模板

```bash
cd packer
./build.sh
```

### 4. 部署基础设施

```bash
# 初始化 Terraform
terraform init

# 查看部署计划
terraform plan

# 应用配置
terraform apply
```

### 5. 配置软路由

```bash
cd ../../ansible
ansible-playbook -i inventory/hosts.yml playbooks/gargantua.yml
```

## 使用 Makefile

项目根目录提供了便捷的 Makefile 命令：

```bash
# 完整部署流程
make gargantua-full-deploy

# 分步操作
make build-gargantua-template  # 构建模板
make gargantua-init           # 初始化
make gargantua-plan           # 查看计划
make gargantua-apply          # 部署基础设施
make deploy-gargantua         # 配置软路由

# 清理
make gargantua-destroy        # 销毁基础设施
```

## 验证部署

### 检查容器状态

```bash
# 检查容器是否运行
ansible gargantua -i ansible/inventory/hosts.yml -m ping

# 查看路由状态
ansible gargantua -i ansible/inventory/hosts.yml -a "/usr/local/bin/gargantua-status"
```

### 验证网络连通性

```bash
# 检查 Tailscale 状态
ansible gargantua -i ansible/inventory/hosts.yml -a "tailscale status"

# 检查路由表
ansible gargantua -i ansible/inventory/hosts.yml -a "ip route show"

# 测试跨网段连通性
ping ************  # 从北京访问天津
ping ************  # 从天津访问北京
```

## 故障排除

### 常见问题

1. **容器无法启动**
   - 检查 Proxmox 资源是否充足
   - 验证 LXC 模板是否正确构建

2. **Tailscale 连接失败**
   - 检查 Headscale 服务器状态
   - 验证预授权密钥是否有效

3. **路由不通**
   - 检查 IP 转发是否启用
   - 验证 iptables 规则是否正确

### 调试命令

```bash
# 查看容器日志
journalctl -u tailscaled -f

# 检查网络接口
ip addr show

# 查看路由表
ip route show table all

# 检查防火墙规则
iptables -L -n -v
iptables -t nat -L -n -v
```

## 配置说明

### 网络配置

- **interstellar 容器 (unsh)**: ************/24，广播 ************/24
- **cooper 容器 (sh)**: ************/24，广播 ************/24
- **Tailscale 接口**: 自动分配，用于跨地域通信

### 安全配置

- 启用 IP 转发
- 配置 NAT 规则
- 防火墙允许 Tailscale 流量
- SSH 密钥认证

## 扩展功能

### 添加新的网络段

1. 在新的 Proxmox 主机上部署容器
2. 更新 Ansible inventory
3. 配置相应的路由广播

### 监控集成

- 集成 Prometheus 监控
- 配置 Grafana 仪表板
- 设置告警规则

## 许可证

本项目采用 MIT 许可证。

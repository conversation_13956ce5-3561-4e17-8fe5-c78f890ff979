# Proxmox 连接配置
proxmox_batata_api_url = "https://192.168.11.100:8006/api2/json"
proxmox_potato_api_url = "https://192.168.10.100:8006/api2/json"
proxmox_user          = "root@pam"
proxmox_password      = "your_proxmox_password"

# Proxmox 节点配置
proxmox_batata_node = "batata"
proxmox_potato_node = "potato"

# 存储配置
proxmox_batata_storage = "local-lvm"
proxmox_potato_storage = "local-lvm"

# LXC 配置
lxc_template = "local:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst"
lxc_password = "your_lxc_root_password"

# SSH 公钥配置
ssh_public_keys = <<EOF
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC... your_ssh_public_key
EOF

# 网络配置
unsh_subnet     = "192.168.11.0/24"
sh_subnet       = "192.168.10.0/24"
qddt_subnet     = "192.168.100.0/24"

# Headscale 配置
headscale_url = "https://headscale.noahgao.net"
